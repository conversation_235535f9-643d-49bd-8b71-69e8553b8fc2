'use client'

import React, { useState, useMemo } from 'react'

import { TrashIcon } from '@radix-ui/react-icons'

import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table'

import { toast } from 'sonner'

import { But<PERSON> } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'

type RowData = Record<string, string | number> & {
  _originalIndex: number
}

interface UploadPreviewDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  data: (string | number)[][]
  onSave: (data: (string | number)[][]) => void
}

export function UploadPreviewDialog({ open, onOpenChange, data, onSave }: UploadPreviewDialogProps) {
  const [uploadedData, setUploadedData] = useState<(string | number)[][]>(data)

  React.useEffect(() => {
    setUploadedData(data)
  }, [data])

  const handleSaveData = () => {
    onSave(uploadedData)
    toast.success('Data saved successfully')
    onOpenChange(false)
  }

  const handleClose = () => {
    onOpenChange(false)
  }

  const handleRemoveRow = React.useCallback(
    (originalIndex: number) => {
      const newData = uploadedData.filter((_, i) => i !== originalIndex)
      setUploadedData(newData)
    },
    [uploadedData]
  )

  const { tableData, columns } = useMemo(() => {
    if (!uploadedData || uploadedData.length === 0) {
      return { tableData: [], columns: [] }
    }

    const headers = uploadedData[0] || []
    const rows = uploadedData.slice(1)

    const cols: ColumnDef<RowData>[] = [
      {
        id: 'actions',
        header: '-',
        cell: ({ row }) => (
          <Button
            variant='ghost'
            size='sm'
            onClick={() => handleRemoveRow(row.original._originalIndex)}
            className='h-6 w-6 p-0 text-red-500 hover:text-red-700'
          >
            <TrashIcon className='h-4 w-4' />
          </Button>
        ),
        enableSorting: false,
        enableHiding: false,
        size: 50,
        meta: {
          className: 'w-12 text-center sticky left-0 bg-background z-20 border-r'
        }
      },
      ...headers.map((header, index) => ({
        id: `col_${index}`,
        accessorKey: `col_${index}`,
        header: String(header),
        cell: ({ row }: { row: { getValue: (key: string) => string | number } }) => (
          <div className='min-w-[150px] whitespace-nowrap'>{row.getValue(`col_${index}`)}</div>
        ),
        enableSorting: false,
        enableHiding: false,
        meta: {
          className: 'min-w-[150px] px-4 whitespace-nowrap'
        }
      }))
    ]

    // Create table data
    const tableRows: RowData[] = rows.map((row, rowIndex) => {
      const rowData: RowData = {
        _originalIndex: rowIndex + 1 // +1 because we skip header
      }
      row.forEach((cell, cellIndex) => {
        rowData[`col_${cellIndex}`] = cell
      })
      return rowData
    })

    return { tableData: tableRows, columns: cols }
  }, [uploadedData, handleRemoveRow])

  const table = useReactTable({
    data: tableData,
    columns,
    getCoreRowModel: getCoreRowModel()
  })

  if (!uploadedData || uploadedData.length === 0) {
    return null
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='h-[525px] w-[1140px] !max-w-[1140px] overflow-hidden p-0'>
        <div className='flex h-full flex-col'>
          {/* Header */}
          <DialogHeader className='shrink-0 border-b px-6 py-4'>
            <div className='flex items-center justify-between'>
              <DialogTitle className='text-xl font-semibold'>Xuất, sửa thực đơn</DialogTitle>
              <div className='flex gap-2'>
                <Button variant='outline' size='sm' onClick={handleClose}>
                  Đóng
                </Button>
                <Button size='sm' className='bg-green-600 hover:bg-green-700' onClick={handleSaveData}>
                  Lưu
                </Button>
              </div>
            </div>
          </DialogHeader>

          {/* Table Container */}
          <div className='flex-1 overflow-hidden p-6'>
            <div className='bg-background h-full w-full overflow-auto rounded-lg border'>
              <div className='min-w-max'>
                <Table>
                  <TableHeader className='bg-muted/50 sticky top-0 z-10'>
                    {table.getHeaderGroups().map(headerGroup => (
                      <TableRow key={headerGroup.id}>
                        {headerGroup.headers.map(header => (
                          <TableHead key={header.id} className={header.column.columnDef.meta?.className || ''}>
                            {header.isPlaceholder
                              ? null
                              : flexRender(header.column.columnDef.header, header.getContext())}
                          </TableHead>
                        ))}
                      </TableRow>
                    ))}
                  </TableHeader>
                  <TableBody>
                    {table.getRowModel().rows?.length ? (
                      table.getRowModel().rows.map(row => (
                        <TableRow key={row.id} className='hover:bg-muted/50'>
                          {row.getVisibleCells().map(cell => (
                            <TableCell key={cell.id} className={cell.column.columnDef.meta?.className || ''}>
                              {flexRender(cell.column.columnDef.cell, cell.getContext())}
                            </TableCell>
                          ))}
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={columns.length} className='h-24 text-center'>
                          No data.
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
